// Type definitions for Cogiteon Player

export interface Config {
  server: ServerConfig;
  slideshow: SlideshowConfig;
  projector: ProjectorConfig;
  kiosk: KioskConfig;
  logging: LoggingConfig;
  audio: AudioConfig;
  monitoring: MonitoringConfig;
}

export interface ServerConfig {
  port: number;
  host: string;
  mediaDirectory: string;
  maxFileSize: string;
  allowedExtensions: {
    video: string[];
    image: string[];
  };
}

export interface SlideshowConfig {
  imageDuration: number;
  transitionDuration: number;
  transitionType: string;
}

export interface ProjectorConfig {
  enabled: boolean;
  ip: string;
  port: number;
  password: string;
  timeout: number;
  retryAttempts: number;
}

export interface KioskConfig {
  chromiumPath: string;
  tmuxSession: string;
  chromiumFlags: string[];
}

export interface LoggingConfig {
  level: string;
  file: string;
  maxSize: string;
  maxFiles: number;
}

export interface AudioConfig {
  enabled: boolean;
  defaultVolume: number;
  fadeInDuration: number;
  fadeOutDuration: number;
}

export interface MonitoringConfig {
  healthCheckInterval: number;
  restartOnError: boolean;
  maxRestartAttempts: number;
}

export interface MediaFile {
  filename: string;
  path: string;
  type: 'video' | 'image';
  size: number;
  duration?: number;
  dimensions?: {
    width: number;
    height: number;
  };
  createdAt: Date;
  modifiedAt: Date;
}

export interface PlaylistItem {
  file: MediaFile;
  duration: number;
  order: number;
}

export interface ProjectorStatus {
  power: 'on' | 'off' | 'warming' | 'cooling' | 'unknown';
  input: string;
  lampHours: number;
  temperature: number;
}

export interface PlayerState {
  currentMedia: MediaFile | null;
  isPlaying: boolean;
  currentTime: number;
  totalTime: number;
  volume: number;
  playlist: PlaylistItem[];
  currentIndex: number;
}

export interface UploadResponse {
  success: boolean;
  message: string;
  file?: MediaFile;
  error?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
