import { Router, Request, Response } from 'express';
import { MediaService } from '../services/media';
import { ConfigService } from '../services/config';
import { LoggerService } from '../services/logger';
import { ApiResponse } from '../../types';

const router = Router();

// Initialize services
const config = ConfigService.loadConfig();
const logger = LoggerService.createLogger(config.logging);
const mediaService = new MediaService(config.server, logger);

/**
 * Get all media files
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const mediaFiles = await mediaService.getMediaFiles();
    const response: ApiResponse = {
      success: true,
      data: mediaFiles
    };
    res.json(response);
  } catch (error) {
    logger.error('Failed to get media files:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to retrieve media files'
    };
    res.status(500).json(response);
  }
});

/**
 * Get media file count
 */
router.get('/count', async (req: Request, res: Response) => {
  try {
    const count = await mediaService.getMediaCount();
    const response: ApiResponse = {
      success: true,
      data: { count }
    };
    res.json(response);
  } catch (error) {
    logger.error('Failed to get media count:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get media count'
    };
    res.status(500).json(response);
  }
});

/**
 * Delete a media file
 */
router.delete('/:filename', async (req: Request, res: Response) => {
  try {
    const { filename } = req.params;
    
    if (!filename) {
      const response: ApiResponse = {
        success: false,
        error: 'Filename is required'
      };
      return res.status(400).json(response);
    }

    // Validate filename (prevent path traversal)
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid filename'
      };
      return res.status(400).json(response);
    }

    const deleted = await mediaService.deleteMediaFile(filename);
    
    if (deleted) {
      const response: ApiResponse = {
        success: true,
        message: `File ${filename} deleted successfully`
      };
      res.json(response);
    } else {
      const response: ApiResponse = {
        success: false,
        error: 'File not found or could not be deleted'
      };
      res.status(404).json(response);
    }
  } catch (error) {
    logger.error('Failed to delete media file:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to delete media file'
    };
    res.status(500).json(response);
  }
});

/**
 * Check media directory status
 */
router.get('/directory/status', async (req: Request, res: Response) => {
  try {
    const status = await mediaService.checkMediaDirectory();
    const response: ApiResponse = {
      success: true,
      data: status
    };
    res.json(response);
  } catch (error) {
    logger.error('Failed to check media directory:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to check media directory status'
    };
    res.status(500).json(response);
  }
});

export default router;
