import fs from 'fs';
import path from 'path';
import { Config } from '../../types';

export class ConfigService {
  private static readonly CONFIG_PATH = path.join(process.cwd(), 'config', 'config.json');
  private static readonly EXAMPLE_CONFIG_PATH = path.join(process.cwd(), 'config', 'config.example.json');

  /**
   * Load configuration from file
   */
  static loadConfig(): Config {
    try {
      // Check if config file exists
      if (!fs.existsSync(this.CONFIG_PATH)) {
        console.log('Config file not found, creating from example...');
        this.createConfigFromExample();
      }

      const configData = fs.readFileSync(this.CONFIG_PATH, 'utf8');
      const config = JSON.parse(configData) as Config;
      
      // Validate configuration
      this.validateConfig(config);
      
      return config;
    } catch (error) {
      throw new Error(`Failed to load configuration: ${error.message}`);
    }
  }

  /**
   * Create config file from example
   */
  private static createConfigFromExample(): void {
    try {
      if (!fs.existsSync(this.EXAMPLE_CONFIG_PATH)) {
        throw new Error('Example config file not found');
      }

      // Ensure config directory exists
      const configDir = path.dirname(this.CONFIG_PATH);
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
      }

      fs.copyFileSync(this.EXAMPLE_CONFIG_PATH, this.CONFIG_PATH);
      console.log('Config file created from example. Please review and update settings.');
    } catch (error) {
      throw new Error(`Failed to create config from example: ${error.message}`);
    }
  }

  /**
   * Validate configuration structure
   */
  private static validateConfig(config: Config): void {
    const requiredFields = [
      'server',
      'slideshow',
      'projector',
      'kiosk',
      'logging',
      'audio',
      'monitoring'
    ];

    for (const field of requiredFields) {
      if (!config[field as keyof Config]) {
        throw new Error(`Missing required configuration field: ${field}`);
      }
    }

    // Validate server config
    if (!config.server.port || !config.server.mediaDirectory) {
      throw new Error('Invalid server configuration');
    }

    // Validate media directory
    if (!fs.existsSync(config.server.mediaDirectory)) {
      console.log(`Creating media directory: ${config.server.mediaDirectory}`);
      fs.mkdirSync(config.server.mediaDirectory, { recursive: true });
    }

    // Validate projector config if enabled
    if (config.projector.enabled) {
      if (!config.projector.ip || !config.projector.port) {
        throw new Error('Invalid projector configuration');
      }
    }
  }

  /**
   * Save configuration to file
   */
  static saveConfig(config: Config): void {
    try {
      this.validateConfig(config);
      const configData = JSON.stringify(config, null, 2);
      fs.writeFileSync(this.CONFIG_PATH, configData, 'utf8');
    } catch (error) {
      throw new Error(`Failed to save configuration: ${error.message}`);
    }
  }

  /**
   * Get configuration file path
   */
  static getConfigPath(): string {
    return this.CONFIG_PATH;
  }

  /**
   * Check if configuration file exists
   */
  static configExists(): boolean {
    return fs.existsSync(this.CONFIG_PATH);
  }

  /**
   * Reload configuration from file
   */
  static reloadConfig(): Config {
    return this.loadConfig();
  }
}
