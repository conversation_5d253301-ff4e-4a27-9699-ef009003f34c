import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import sharp from 'sharp';
import ffmpeg from 'fluent-ffmpeg';
import { lookup } from 'mime-types';
import winston from 'winston';

import { MediaFile, ServerConfig } from '../../types';

const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const unlink = promisify(fs.unlink);

export class MediaService {
  private config: ServerConfig;
  private logger: winston.Logger;

  constructor(config: ServerConfig, logger: winston.Logger) {
    this.config = config;
    this.logger = logger;
  }

  /**
   * Get all media files from the media directory
   */
  async getMediaFiles(): Promise<MediaFile[]> {
    try {
      const files = await readdir(this.config.mediaDirectory);
      const mediaFiles: MediaFile[] = [];

      for (const filename of files) {
        const filePath = path.join(this.config.mediaDirectory, filename);
        const stats = await stat(filePath);

        if (stats.isFile() && this.isMediaFile(filename)) {
          const mediaFile = await this.createMediaFileInfo(filename, filePath, stats);
          if (mediaFile) {
            mediaFiles.push(mediaFile);
          }
        }
      }

      // Sort by creation date (newest first)
      mediaFiles.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      this.logger.info(`Found ${mediaFiles.length} media files`);
      return mediaFiles;
    } catch (error) {
      this.logger.error('Failed to get media files:', error);
      return [];
    }
  }

  /**
   * Get count of media files
   */
  async getMediaCount(): Promise<number> {
    try {
      const files = await this.getMediaFiles();
      return files.length;
    } catch (error) {
      this.logger.error('Failed to get media count:', error);
      return 0;
    }
  }

  /**
   * Delete a media file
   */
  async deleteMediaFile(filename: string): Promise<boolean> {
    try {
      const filePath = path.join(this.config.mediaDirectory, filename);
      
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        this.logger.warn(`File not found: ${filename}`);
        return false;
      }

      await unlink(filePath);
      this.logger.info(`Deleted media file: ${filename}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete media file ${filename}:`, error);
      return false;
    }
  }

  /**
   * Check if file is a supported media file
   */
  private isMediaFile(filename: string): boolean {
    const ext = path.extname(filename).toLowerCase();
    const allExtensions = [
      ...this.config.allowedExtensions.video,
      ...this.config.allowedExtensions.image
    ];
    return allExtensions.includes(ext);
  }

  /**
   * Determine media type from file extension
   */
  private getMediaType(filename: string): 'video' | 'image' | null {
    const ext = path.extname(filename).toLowerCase();
    
    if (this.config.allowedExtensions.video.includes(ext)) {
      return 'video';
    }
    
    if (this.config.allowedExtensions.image.includes(ext)) {
      return 'image';
    }
    
    return null;
  }

  /**
   * Create MediaFile info object
   */
  private async createMediaFileInfo(filename: string, filePath: string, stats: fs.Stats): Promise<MediaFile | null> {
    try {
      const mediaType = this.getMediaType(filename);
      if (!mediaType) {
        return null;
      }

      const mediaFile: MediaFile = {
        filename,
        path: filePath,
        type: mediaType,
        size: stats.size,
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime
      };

      // Get additional metadata based on type
      if (mediaType === 'image') {
        const imageInfo = await this.getImageInfo(filePath);
        if (imageInfo) {
          mediaFile.dimensions = imageInfo;
        }
      } else if (mediaType === 'video') {
        const videoInfo = await this.getVideoInfo(filePath);
        if (videoInfo) {
          mediaFile.duration = videoInfo.duration;
          mediaFile.dimensions = videoInfo.dimensions;
        }
      }

      return mediaFile;
    } catch (error) {
      this.logger.error(`Failed to create media file info for ${filename}:`, error);
      return null;
    }
  }

  /**
   * Get image dimensions using Sharp
   */
  private async getImageInfo(filePath: string): Promise<{ width: number; height: number } | null> {
    try {
      const metadata = await sharp(filePath).metadata();
      if (metadata.width && metadata.height) {
        return {
          width: metadata.width,
          height: metadata.height
        };
      }
      return null;
    } catch (error) {
      this.logger.error(`Failed to get image info for ${filePath}:`, error);
      return null;
    }
  }

  /**
   * Get video info using FFmpeg
   */
  private async getVideoInfo(filePath: string): Promise<{ duration: number; dimensions: { width: number; height: number } } | null> {
    return new Promise((resolve) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          this.logger.error(`Failed to get video info for ${filePath}:`, err);
          resolve(null);
          return;
        }

        try {
          const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
          if (!videoStream) {
            resolve(null);
            return;
          }

          const duration = metadata.format.duration || 0;
          const dimensions = {
            width: videoStream.width || 0,
            height: videoStream.height || 0
          };

          resolve({ duration, dimensions });
        } catch (error) {
          this.logger.error(`Failed to parse video metadata for ${filePath}:`, error);
          resolve(null);
        }
      });
    });
  }

  /**
   * Validate uploaded file
   */
  validateUploadedFile(file: Express.Multer.File): { valid: boolean; error?: string } {
    // Check file size
    const maxSize = this.parseSize(this.config.maxFileSize);
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File size exceeds maximum allowed size of ${this.config.maxFileSize}`
      };
    }

    // Check file extension
    if (!this.isMediaFile(file.originalname)) {
      return {
        valid: false,
        error: 'File type not supported'
      };
    }

    // Check MIME type
    const expectedMimeType = lookup(file.originalname);
    if (!expectedMimeType || !file.mimetype.startsWith(expectedMimeType.split('/')[0])) {
      return {
        valid: false,
        error: 'File MIME type does not match extension'
      };
    }

    return { valid: true };
  }

  /**
   * Parse size string to bytes
   */
  private parseSize(sizeStr: string): number {
    const units: { [key: string]: number } = {
      'B': 1,
      'KB': 1024,
      'MB': 1024 * 1024,
      'GB': 1024 * 1024 * 1024
    };

    const match = sizeStr.match(/^(\d+)\s*(B|KB|MB|GB)$/i);
    if (!match) {
      return 100 * 1024 * 1024; // Default 100MB
    }

    const [, size, unit] = match;
    return parseInt(size) * units[unit.toUpperCase()];
  }

  /**
   * Get media directory path
   */
  getMediaDirectory(): string {
    return this.config.mediaDirectory;
  }

  /**
   * Check if media directory exists and is writable
   */
  async checkMediaDirectory(): Promise<{ exists: boolean; writable: boolean; error?: string }> {
    try {
      const exists = fs.existsSync(this.config.mediaDirectory);
      if (!exists) {
        return { exists: false, writable: false, error: 'Media directory does not exist' };
      }

      // Test write access
      const testFile = path.join(this.config.mediaDirectory, '.write-test');
      try {
        fs.writeFileSync(testFile, 'test');
        fs.unlinkSync(testFile);
        return { exists: true, writable: true };
      } catch (error) {
        return { exists: true, writable: false, error: 'Media directory is not writable' };
      }
    } catch (error) {
      return { exists: false, writable: false, error: error.message };
    }
  }
}
