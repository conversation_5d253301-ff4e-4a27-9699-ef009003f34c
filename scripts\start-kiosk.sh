#!/bin/bash

# Cogiteon Player Kiosk Mode Startup Script
# This script starts the media player in fullscreen kiosk mode using tmux

set -e

# Configuration
CONFIG_FILE="config/config.json"
TMUX_SESSION="cogiteon-player"
SERVER_URL="http://localhost:3000"
LOG_FILE="logs/kiosk.log"

# Create logs directory if it doesn't exist
mkdir -p logs

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to check if server is running
check_server() {
    local retries=0
    local max_retries=30
    
    while [ $retries -lt $max_retries ]; do
        if curl -s "$SERVER_URL" > /dev/null 2>&1; then
            return 0
        fi
        log "Waiting for server to start... (attempt $((retries + 1))/$max_retries)"
        sleep 2
        retries=$((retries + 1))
    done
    
    log "ERROR: Server failed to start after $max_retries attempts"
    return 1
}

# Function to get configuration value
get_config() {
    local key="$1"
    if [ -f "$CONFIG_FILE" ]; then
        node -e "console.log(JSON.parse(require('fs').readFileSync('$CONFIG_FILE', 'utf8')).$key)" 2>/dev/null || echo ""
    fi
}

# Function to start the server
start_server() {
    log "Starting Cogiteon Player server..."
    
    # Check if server is already running
    if pgrep -f "node.*app.js" > /dev/null; then
        log "Server is already running"
        return 0
    fi
    
    # Start server in background
    npm start > logs/server.log 2>&1 &
    local server_pid=$!
    
    log "Server started with PID: $server_pid"
    
    # Wait for server to be ready
    if check_server; then
        log "Server is ready"
        return 0
    else
        log "ERROR: Server failed to start properly"
        return 1
    fi
}

# Function to start chromium in kiosk mode
start_kiosk() {
    local chromium_path=$(get_config "kiosk.chromiumPath")
    local chromium_flags=$(get_config "kiosk.chromiumFlags")
    
    # Default chromium path if not configured
    if [ -z "$chromium_path" ]; then
        chromium_path="/usr/bin/chromium-browser"
    fi
    
    # Check if chromium exists
    if [ ! -f "$chromium_path" ]; then
        log "ERROR: Chromium not found at $chromium_path"
        log "Please install chromium-browser or update the path in config.json"
        return 1
    fi
    
    log "Starting Chromium in kiosk mode..."
    
    # Kill existing tmux session if it exists
    tmux kill-session -t "$TMUX_SESSION" 2>/dev/null || true
    
    # Start new tmux session with chromium
    tmux new-session -d -s "$TMUX_SESSION" \
        "$chromium_path --kiosk --no-sandbox --disable-web-security --disable-features=VizDisplayCompositor --start-fullscreen --autoplay-policy=no-user-gesture-required --disable-infobars --disable-session-crashed-bubble --disable-restore-session-state $SERVER_URL"
    
    log "Kiosk mode started in tmux session: $TMUX_SESSION"
    log "To view the session: tmux attach-session -t $TMUX_SESSION"
    log "To stop kiosk mode: tmux kill-session -t $TMUX_SESSION"
}

# Function to setup environment
setup_environment() {
    log "Setting up environment..."
    
    # Create media directory if it doesn't exist
    local media_dir=$(get_config "server.mediaDirectory")
    if [ -z "$media_dir" ]; then
        media_dir="/media"
    fi
    
    if [ ! -d "$media_dir" ]; then
        log "Creating media directory: $media_dir"
        sudo mkdir -p "$media_dir"
        sudo chown $USER:$USER "$media_dir"
    fi
    
    # Copy config file if it doesn't exist
    if [ ! -f "$CONFIG_FILE" ]; then
        log "Creating config file from example..."
        cp config/config.example.json "$CONFIG_FILE"
    fi
    
    # Build the application if needed
    if [ ! -d "dist" ]; then
        log "Building application..."
        npm run build
    fi
}

# Function to cleanup on exit
cleanup() {
    log "Cleaning up..."
    tmux kill-session -t "$TMUX_SESSION" 2>/dev/null || true
}

# Main execution
main() {
    log "Starting Cogiteon Player in kiosk mode..."
    
    # Setup cleanup trap
    trap cleanup EXIT
    
    # Setup environment
    setup_environment
    
    # Start server
    if ! start_server; then
        log "ERROR: Failed to start server"
        exit 1
    fi
    
    # Start kiosk mode
    if ! start_kiosk; then
        log "ERROR: Failed to start kiosk mode"
        exit 1
    fi
    
    log "Cogiteon Player is now running in kiosk mode"
    log "Press Ctrl+C to stop"
    
    # Keep script running
    while true; do
        sleep 10
        # Check if tmux session is still running
        if ! tmux has-session -t "$TMUX_SESSION" 2>/dev/null; then
            log "WARNING: Kiosk session has ended"
            break
        fi
    done
}

# Run main function
main "$@"
