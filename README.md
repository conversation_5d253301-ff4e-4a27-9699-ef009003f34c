# Cogiteon Player

A TypeScript-based media player application designed for Raspberry Pi, featuring fullscreen kiosk mode playback, web-based file management, and projector control via PJLink.

## Features

- **Fullscreen Kiosk Mode**: Chromium-based player running in tmux for reliable operation
- **Smart Media Playback**: Automatically handles videos, images, and slideshows from `/media` directory
- **Web Management Interface**: Upload, view, and delete media files via `/manage` endpoint
- **Projector Control**: PJLink communication for remote projector power management
- **Audio Control**: Open sound control integration
- **Configurable**: JSON-based configuration for slideshow timing, projector settings, etc.

## Project Structure

```
cogiteon-player/
├── src/
│   ├── server/           # Express.js backend
│   │   ├── app.ts        # Main server application
│   │   ├── routes/       # API routes
│   │   ├── services/     # Business logic
│   │   └── utils/        # Utility functions
│   ├── client/           # Frontend (served to kiosk)
│   │   ├── player.html   # Main player interface
│   │   ├── manage.html   # File management interface
│   │   ├── css/          # Stylesheets
│   │   └── js/           # Client-side JavaScript
│   └── types/            # TypeScript type definitions
├── media/                # Media files directory
├── config/
│   └── config.json       # Application configuration
├── scripts/              # Deployment and startup scripts
├── package.json
├── tsconfig.json
└── README.md
```

## Technology Stack

- **Backend**: Node.js + Express.js + TypeScript
- **Frontend**: Vanilla HTML/CSS/JS (for minimal overhead)
- **File Upload**: Multer middleware
- **Media Processing**: FFmpeg for video info, Sharp for images
- **Process Management**: PM2 for production deployment
- **Kiosk Mode**: Chromium with custom flags
- **Session Management**: tmux for reliable browser sessions

## Configuration

The application uses a JSON configuration file (`config/config.json`):

```json
{
  "server": {
    "port": 3000,
    "mediaDirectory": "/media"
  },
  "slideshow": {
    "imageDuration": 5000,
    "transitionDuration": 1000
  },
  "projector": {
    "enabled": true,
    "ip": "*************",
    "port": 4352,
    "password": "admin"
  },
  "kiosk": {
    "chromiumPath": "/usr/bin/chromium-browser",
    "tmuxSession": "cogiteon-player"
  }
}
```

## Installation & Setup

### Prerequisites
- Raspberry Pi OS (Bullseye or newer)
- Node.js 18+ and npm
- Chromium browser
- tmux
- FFmpeg (for video processing)

### Installation Steps

1. **Clone and install dependencies**:
   ```bash
   git clone <repository-url> cogiteon-player
   cd cogiteon-player
   npm install
   ```

2. **Build the application**:
   ```bash
   npm run build
   ```

3. **Configure the application**:
   ```bash
   cp config/config.example.json config/config.json
   # Edit config.json with your settings
   ```

4. **Create media directory**:
   ```bash
   sudo mkdir -p /media
   sudo chown $USER:$USER /media
   ```

5. **Install as system service**:
   ```bash
   sudo npm run install-service
   ```

## Usage

### Starting the Application

**Development mode**:
```bash
npm run dev
```

**Production mode**:
```bash
npm start
```

**Kiosk mode** (fullscreen on Raspberry Pi):
```bash
npm run kiosk
```

### Media Management

1. Access the management interface at `http://localhost:3000/manage`
2. Upload media files (videos: mp4, webm; images: jpg, png, gif)
3. View current media files and their status
4. Delete unwanted files

### Media Playback Logic

- **Single video file**: Loops continuously
- **Single image**: Displays indefinitely
- **Multiple images**: Slideshow with configurable timing
- **Mixed media**: Videos play first, then images in slideshow
- **No media**: Shows placeholder screen

### Projector Control

Send HTTP requests to control projector:
- `POST /projector/on` - Turn projector on
- `POST /projector/off` - Turn projector off
- `GET /projector/status` - Get projector status

## API Endpoints

- `GET /` - Main player interface
- `GET /manage` - File management interface
- `POST /upload` - Upload media files
- `GET /api/media` - List media files
- `DELETE /api/media/:filename` - Delete media file
- `POST /projector/on` - Turn projector on
- `POST /projector/off` - Turn projector off
- `GET /projector/status` - Get projector status

## Development

### Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build TypeScript to JavaScript
- `npm run start` - Start production server
- `npm run kiosk` - Start in kiosk mode
- `npm run test` - Run tests
- `npm run lint` - Run ESLint

### File Structure Details

- **Server**: Express.js application handling API routes and static file serving
- **Client**: Minimal frontend for media playback and file management
- **Services**: Business logic for media handling, PJLink communication
- **Scripts**: Bash scripts for system integration and kiosk mode

## Deployment on Raspberry Pi

1. **Auto-start configuration**: The application can be configured to start automatically on boot
2. **Kiosk mode**: Chromium runs in fullscreen kiosk mode within a tmux session
3. **Process monitoring**: PM2 ensures the application restarts on crashes
4. **Log management**: Logs are rotated and stored in `/var/log/cogiteon-player/`

## Troubleshooting

### Common Issues

1. **Media not playing**: Check file permissions in `/media` directory
2. **Projector not responding**: Verify IP address and PJLink settings
3. **Kiosk mode not starting**: Ensure tmux and chromium are installed
4. **Upload failing**: Check disk space and file size limits

### Logs

- Application logs: `tail -f logs/app.log`
- PM2 logs: `pm2 logs cogiteon-player`
- System logs: `journalctl -u cogiteon-player`

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request
