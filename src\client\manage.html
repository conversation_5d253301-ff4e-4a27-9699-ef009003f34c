<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cogiteon Player - Management</title>
    <link rel="stylesheet" href="/static/css/manage.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Cogiteon Player Management</h1>
            <div class="status-bar">
                <span id="connection-status" class="status-indicator">●</span>
                <span id="media-count">0 files</span>
                <button id="refresh-btn" class="btn btn-secondary">Refresh</button>
            </div>
        </header>

        <main class="main-content">
            <!-- Upload Section -->
            <section class="upload-section">
                <h2>Upload Media Files</h2>
                <div class="upload-area" id="upload-area">
                    <div class="upload-content">
                        <div class="upload-icon">📁</div>
                        <p>Drag and drop files here or click to browse</p>
                        <p class="upload-hint">Supported: MP4, WebM, AVI, MOV, JPG, PNG, GIF</p>
                        <input type="file" id="file-input" multiple accept="video/*,image/*" style="display: none;">
                        <button id="browse-btn" class="btn btn-primary">Browse Files</button>
                    </div>
                </div>

                <div id="upload-progress" class="upload-progress" style="display: none;">
                    <div class="progress-bar">
                        <div id="progress-fill" class="progress-fill"></div>
                    </div>
                    <span id="progress-text">Uploading...</span>
                </div>
            </section>

            <!-- Media Files Section -->
            <section class="media-section">
                <h2>Media Files</h2>
                <div class="media-controls">
                    <button id="select-all-btn" class="btn btn-secondary">Select All</button>
                    <button id="delete-selected-btn" class="btn btn-danger" disabled>Delete Selected</button>
                    <div class="sort-controls">
                        <label for="sort-select">Sort by:</label>
                        <select id="sort-select">
                            <option value="name">Name</option>
                            <option value="date" selected>Date</option>
                            <option value="size">Size</option>
                            <option value="type">Type</option>
                        </select>
                    </div>
                </div>

                <div id="media-list" class="media-list">
                    <div class="loading-spinner" id="loading-spinner">Loading...</div>
                </div>
            </section>

            <!-- Projector Control Section -->
            <section class="projector-section" id="projector-section" style="display: none;">
                <h2>Projector Control</h2>
                <div class="projector-controls">
                    <button id="projector-on-btn" class="btn btn-success">Power On</button>
                    <button id="projector-off-btn" class="btn btn-warning">Power Off</button>
                    <button id="projector-status-btn" class="btn btn-secondary">Check Status</button>
                </div>
                <div id="projector-status" class="projector-status"></div>
            </section>

            <!-- System Info Section -->
            <section class="system-section">
                <h2>System Information</h2>
                <div id="system-info" class="system-info">
                    <div class="info-item">
                        <span class="info-label">Server Status:</span>
                        <span id="server-status" class="info-value">Checking...</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Media Directory:</span>
                        <span id="media-directory" class="info-value">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Disk Space:</span>
                        <span id="disk-space" class="info-value">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Uptime:</span>
                        <span id="uptime" class="info-value">-</span>
                    </div>
                </div>
            </section>
        </main>

        <!-- Toast Notifications -->
        <div id="toast-container" class="toast-container"></div>

        <!-- Confirmation Modal -->
        <div id="confirm-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <h3>Confirm Action</h3>
                <p id="confirm-message">Are you sure?</p>
                <div class="modal-actions">
                    <button id="confirm-cancel" class="btn btn-secondary">Cancel</button>
                    <button id="confirm-ok" class="btn btn-danger">Confirm</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Management interface JavaScript will be embedded here
        // This will be replaced with external file in production
    </script>
    <script src="/static/js/manage.js"></script>
</body>
</html>
