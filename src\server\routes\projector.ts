import { Router, Request, Response } from 'express';
import { PJLinkService } from '../services/pjlink';
import { ConfigService } from '../services/config';
import { LoggerService } from '../services/logger';
import { ApiResponse } from '../../types';

const router = Router();

// Initialize services
const config = ConfigService.loadConfig();
const logger = LoggerService.createLogger(config.logging);
let pjlinkService: PJLinkService | null = null;

if (config.projector.enabled) {
  pjlinkService = new PJLinkService(config.projector);
}

/**
 * Middleware to check if projector service is available
 */
const checkProjectorService = (req: Request, res: Response, next: any) => {
  if (!pjlinkService) {
    const response: ApiResponse = {
      success: false,
      error: 'Projector service is not enabled'
    };
    return res.status(503).json(response);
  }
  next();
};

/**
 * Turn projector on
 */
router.post('/on', checkProjectorService, async (req: Request, res: Response) => {
  try {
    const success = await pjlinkService!.powerOn();
    const response: ApiResponse = {
      success,
      message: success ? 'Projector power on command sent' : 'Failed to turn projector on'
    };
    res.json(response);
  } catch (error) {
    logger.error('Failed to turn projector on:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to communicate with projector'
    };
    res.status(500).json(response);
  }
});

/**
 * Turn projector off
 */
router.post('/off', checkProjectorService, async (req: Request, res: Response) => {
  try {
    const success = await pjlinkService!.powerOff();
    const response: ApiResponse = {
      success,
      message: success ? 'Projector power off command sent' : 'Failed to turn projector off'
    };
    res.json(response);
  } catch (error) {
    logger.error('Failed to turn projector off:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to communicate with projector'
    };
    res.status(500).json(response);
  }
});

/**
 * Get projector status
 */
router.get('/status', checkProjectorService, async (req: Request, res: Response) => {
  try {
    const status = await pjlinkService!.getStatus();
    const response: ApiResponse = {
      success: true,
      data: status
    };
    res.json(response);
  } catch (error) {
    logger.error('Failed to get projector status:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get projector status'
    };
    res.status(500).json(response);
  }
});

/**
 * Get projector power status only
 */
router.get('/power', checkProjectorService, async (req: Request, res: Response) => {
  try {
    const power = await pjlinkService!.getPowerStatus();
    const response: ApiResponse = {
      success: true,
      data: { power }
    };
    res.json(response);
  } catch (error) {
    logger.error('Failed to get projector power status:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get projector power status'
    };
    res.status(500).json(response);
  }
});

/**
 * Set projector input source
 */
router.post('/input/:source', checkProjectorService, async (req: Request, res: Response) => {
  try {
    const { source } = req.params;
    
    if (!source) {
      const response: ApiResponse = {
        success: false,
        error: 'Input source is required'
      };
      return res.status(400).json(response);
    }

    const success = await pjlinkService!.setInputSource(source);
    const response: ApiResponse = {
      success,
      message: success ? `Input source set to ${source}` : 'Failed to set input source'
    };
    res.json(response);
  } catch (error) {
    logger.error('Failed to set projector input:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to set projector input'
    };
    res.status(500).json(response);
  }
});

/**
 * Test projector connection
 */
router.get('/test', checkProjectorService, async (req: Request, res: Response) => {
  try {
    const connected = await pjlinkService!.testConnection();
    const response: ApiResponse = {
      success: true,
      data: { 
        connected,
        message: connected ? 'Projector is reachable' : 'Projector is not reachable'
      }
    };
    res.json(response);
  } catch (error) {
    logger.error('Failed to test projector connection:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to test projector connection'
    };
    res.status(500).json(response);
  }
});

/**
 * Send custom PJLink command
 */
router.post('/command', checkProjectorService, async (req: Request, res: Response) => {
  try {
    const { command } = req.body;
    
    if (!command) {
      const response: ApiResponse = {
        success: false,
        error: 'Command is required'
      };
      return res.status(400).json(response);
    }

    const result = await pjlinkService!.sendCustomCommand(command);
    const response: ApiResponse = {
      success: true,
      data: { command, result }
    };
    res.json(response);
  } catch (error) {
    logger.error('Failed to send custom command:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to send custom command'
    };
    res.status(500).json(response);
  }
});

export default router;
