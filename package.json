{"name": "cogiteon-player", "version": "1.0.0", "description": "TypeScript-based media player for Raspberry Pi with kiosk mode and projector control", "main": "dist/server/app.js", "scripts": {"dev": "concurrently \"npm run build:watch\" \"npm run start:dev\"", "build": "tsc", "build:watch": "tsc --watch", "start": "node dist/server/app.js", "start:dev": "nodemon dist/server/app.js", "kiosk": "bash scripts/start-kiosk.sh", "install-service": "bash scripts/install-service.sh", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["media-player", "raspberry-pi", "kiosk", "pjlink", "typescript", "chromium"], "author": "Cogiteon Player Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "winston": "^3.11.0", "node-cron": "^3.0.3", "ws": "^8.14.2", "sharp": "^0.32.6", "fluent-ffmpeg": "^2.1.2", "mime-types": "^2.1.35", "express-rate-limit": "^7.1.5"}, "devDependencies": {"@types/node": "^20.8.0", "@types/express": "^4.17.20", "@types/multer": "^1.4.8", "@types/cors": "^2.8.15", "@types/compression": "^1.7.4", "@types/ws": "^8.5.8", "@types/fluent-ffmpeg": "^2.1.24", "@types/mime-types": "^2.1.4", "@types/jest": "^29.5.6", "@typescript-eslint/eslint-plugin": "^6.8.0", "@typescript-eslint/parser": "^6.8.0", "typescript": "^5.2.2", "nodemon": "^3.0.1", "concurrently": "^8.2.2", "eslint": "^8.51.0", "jest": "^29.7.0", "ts-jest": "^29.1.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}