/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    color: #2c3e50;
    font-size: 24px;
    font-weight: 600;
}

.status-bar {
    display: flex;
    align-items: center;
    gap: 15px;
}

.status-indicator {
    font-size: 12px;
    color: #27ae60;
}

.status-indicator.disconnected {
    color: #e74c3c;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-block;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

/* Main content */
.main-content {
    display: grid;
    gap: 20px;
}

section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

section h2 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 600;
}

/* Upload section */
.upload-area {
    border: 2px dashed #bdc3c7;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s;
    cursor: pointer;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: #3498db;
    background-color: #f8f9fa;
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.upload-icon {
    font-size: 48px;
    opacity: 0.5;
}

.upload-hint {
    font-size: 12px;
    color: #7f8c8d;
}

.upload-progress {
    margin-top: 15px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #ecf0f1;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #3498db;
    transition: width 0.3s;
    width: 0%;
}

/* Media section */
.media-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.sort-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.sort-controls select {
    padding: 4px 8px;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    font-size: 14px;
}

.media-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    min-height: 200px;
}

.media-item {
    border: 1px solid #ecf0f1;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.2s;
    cursor: pointer;
}

.media-item:hover {
    border-color: #3498db;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.media-item.selected {
    border-color: #3498db;
    background-color: #f8f9fa;
}

.media-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.media-checkbox {
    margin-right: 8px;
}

.media-type {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.media-type.video {
    background: #e8f5e8;
    color: #27ae60;
}

.media-type.image {
    background: #fff3cd;
    color: #856404;
}

.media-filename {
    font-weight: 500;
    margin-bottom: 5px;
    word-break: break-word;
}

.media-info {
    font-size: 12px;
    color: #7f8c8d;
    display: flex;
    justify-content: space-between;
}

.media-actions {
    margin-top: 10px;
    display: flex;
    gap: 5px;
}

.media-actions .btn {
    padding: 4px 8px;
    font-size: 12px;
}

/* Projector section */
.projector-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.projector-status {
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    font-family: monospace;
    font-size: 14px;
}

/* System info */
.system-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-label {
    font-weight: 600;
    color: #7f8c8d;
    font-size: 12px;
    text-transform: uppercase;
}

.info-value {
    font-size: 14px;
    color: #2c3e50;
}

/* Loading spinner */
.loading-spinner {
    text-align: center;
    padding: 40px;
    color: #7f8c8d;
}

/* Toast notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    padding: 12px 16px;
    border-radius: 4px;
    color: white;
    font-weight: 500;
    min-width: 250px;
    animation: slideIn 0.3s ease;
}

.toast.success {
    background: #27ae60;
}

.toast.error {
    background: #e74c3c;
}

.toast.warning {
    background: #f39c12;
}

.toast.info {
    background: #3498db;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 20px;
    border-radius: 8px;
    max-width: 400px;
    width: 90%;
}

.modal-content h3 {
    margin-bottom: 10px;
    color: #2c3e50;
}

.modal-content p {
    margin-bottom: 20px;
    color: #7f8c8d;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .media-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .media-list {
        grid-template-columns: 1fr;
    }
    
    .projector-controls {
        flex-direction: column;
    }
    
    .system-info {
        grid-template-columns: 1fr;
    }
}
