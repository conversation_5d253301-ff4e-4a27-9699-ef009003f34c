import { Router } from 'express';
import { ApiResponse } from '../../types';

const router = Router();

/**
 * API status endpoint
 */
router.get('/', (req, res) => {
  const response: ApiResponse = {
    success: true,
    data: {
      name: 'Cogiteon Player API',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      endpoints: {
        'GET /api': 'API information',
        'GET /api/media': 'List media files',
        'POST /upload': 'Upload media file',
        'DELETE /api/media/:filename': 'Delete media file',
        'POST /projector/on': 'Turn projector on',
        'POST /projector/off': 'Turn projector off',
        'GET /projector/status': 'Get projector status',
        'GET /health': 'Health check'
      }
    }
  };
  res.json(response);
});

/**
 * API version endpoint
 */
router.get('/version', (req, res) => {
  const response: ApiResponse = {
    success: true,
    data: {
      version: '1.0.0',
      build: process.env.BUILD_NUMBER || 'development',
      node: process.version,
      platform: process.platform,
      arch: process.arch
    }
  };
  res.json(response);
});

export default router;
