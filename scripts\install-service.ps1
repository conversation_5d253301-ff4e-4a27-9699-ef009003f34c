# Cogiteon Player Service Installation Script for Windows
# This script sets up the media player as a Windows service using PM2

param(
    [switch]$Uninstall,
    [string]$ServiceName = "cogiteon-player"
)

# Configuration
$ProjectRoot = Split-Path -Parent $PSScriptRoot
$ConfigFile = Join-Path $ProjectRoot "config\config.json"
$ExampleConfig = Join-Path $ProjectRoot "config\config.example.json"

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message"
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Install-Dependencies {
    Write-Log "Installing dependencies..."
    
    # Check if Node.js is installed
    try {
        $nodeVersion = node --version
        Write-Log "Node.js version: $nodeVersion"
    }
    catch {
        Write-Log "Node.js is not installed. Please install Node.js 18+ first." "ERROR"
        exit 1
    }
    
    # Check if PM2 is installed globally
    try {
        $pm2Version = pm2 --version
        Write-Log "PM2 version: $pm2Version"
    }
    catch {
        Write-Log "Installing PM2 globally..."
        npm install -g pm2
        npm install -g pm2-windows-service
    }
    
    # Install project dependencies
    Write-Log "Installing project dependencies..."
    Set-Location $ProjectRoot
    npm install
    
    # Build the project
    Write-Log "Building the project..."
    npm run build
}

function Setup-Configuration {
    Write-Log "Setting up configuration..."
    
    # Create config file if it doesn't exist
    if (-not (Test-Path $ConfigFile)) {
        Write-Log "Creating config file from example..."
        Copy-Item $ExampleConfig $ConfigFile
        Write-Log "Please edit $ConfigFile with your specific settings"
    }
    
    # Create media directory
    $config = Get-Content $ConfigFile | ConvertFrom-Json
    $mediaDir = $config.server.mediaDirectory
    
    if (-not (Test-Path $mediaDir)) {
        Write-Log "Creating media directory: $mediaDir"
        New-Item -ItemType Directory -Force -Path $mediaDir | Out-Null
    }
    
    # Create logs directory
    $logsDir = Join-Path $ProjectRoot "logs"
    if (-not (Test-Path $logsDir)) {
        Write-Log "Creating logs directory: $logsDir"
        New-Item -ItemType Directory -Force -Path $logsDir | Out-Null
    }
}

function Install-Service {
    Write-Log "Installing Cogiteon Player as Windows service..."
    
    # Stop existing PM2 processes
    try {
        pm2 stop $ServiceName
        pm2 delete $ServiceName
    }
    catch {
        # Ignore errors if service doesn't exist
    }
    
    # Start the application with PM2
    $appScript = Join-Path $ProjectRoot "dist\server\app.js"
    pm2 start $appScript --name $ServiceName --time
    
    # Save PM2 configuration
    pm2 save
    
    # Install PM2 as Windows service
    pm2-service-install -n $ServiceName
    
    Write-Log "Service installed successfully!"
    Write-Log "You can manage the service using:"
    Write-Log "  pm2 start $ServiceName"
    Write-Log "  pm2 stop $ServiceName"
    Write-Log "  pm2 restart $ServiceName"
    Write-Log "  pm2 logs $ServiceName"
}

function Uninstall-Service {
    Write-Log "Uninstalling Cogiteon Player service..."
    
    # Stop and delete PM2 process
    try {
        pm2 stop $ServiceName
        pm2 delete $ServiceName
        pm2 save
    }
    catch {
        Write-Log "No PM2 process found to remove"
    }
    
    # Uninstall Windows service
    try {
        pm2-service-uninstall
    }
    catch {
        Write-Log "No Windows service found to remove"
    }
    
    Write-Log "Service uninstalled successfully!"
}

function Show-Usage {
    Write-Host @"
Cogiteon Player Service Installation Script

Usage:
    .\install-service.ps1                 # Install the service
    .\install-service.ps1 -Uninstall      # Uninstall the service
    .\install-service.ps1 -ServiceName "custom-name"  # Use custom service name

Prerequisites:
    - Node.js 18+ installed
    - Run as Administrator
    - Project built (npm run build)

"@
}

# Main execution
if (-not (Test-Administrator)) {
    Write-Log "This script must be run as Administrator" "ERROR"
    exit 1
}

if ($Uninstall) {
    Uninstall-Service
}
else {
    Write-Log "Starting Cogiteon Player service installation..."
    Install-Dependencies
    Setup-Configuration
    Install-Service
    Write-Log "Installation completed!"
    Write-Log ""
    Write-Log "Next steps:"
    Write-Log "1. Edit config\config.json with your settings"
    Write-Log "2. Add media files to the media directory"
    Write-Log "3. Start kiosk mode: npm run kiosk"
    Write-Log "4. Access management interface: http://localhost:3000/manage"
}
