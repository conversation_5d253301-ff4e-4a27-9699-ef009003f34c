import winston from 'winston';
import path from 'path';
import fs from 'fs';
import { LoggingConfig } from '../../types';

export class LoggerService {
  /**
   * Create a Winston logger instance
   */
  static createLogger(config: LoggingConfig): winston.Logger {
    // Ensure logs directory exists
    const logDir = path.dirname(config.file);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    const logger = winston.createLogger({
      level: config.level,
      format: winston.format.combine(
        winston.format.timestamp({
          format: 'YYYY-MM-DD HH:mm:ss'
        }),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
      defaultMeta: { service: 'cogiteon-player' },
      transports: [
        // File transport
        new winston.transports.File({
          filename: config.file,
          maxsize: this.parseSize(config.maxSize),
          maxFiles: config.maxFiles,
          tailable: true
        }),
        
        // Error file transport
        new winston.transports.File({
          filename: path.join(logDir, 'error.log'),
          level: 'error',
          maxsize: this.parseSize(config.maxSize),
          maxFiles: config.maxFiles,
          tailable: true
        })
      ]
    });

    // Add console transport in development
    if (process.env.NODE_ENV !== 'production') {
      logger.add(new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.simple(),
          winston.format.printf(({ timestamp, level, message, ...meta }) => {
            let log = `${timestamp} [${level}]: ${message}`;
            if (Object.keys(meta).length > 0) {
              log += ` ${JSON.stringify(meta)}`;
            }
            return log;
          })
        )
      }));
    }

    return logger;
  }

  /**
   * Parse size string to bytes
   */
  private static parseSize(sizeStr: string): number {
    const units: { [key: string]: number } = {
      'B': 1,
      'KB': 1024,
      'MB': 1024 * 1024,
      'GB': 1024 * 1024 * 1024
    };

    const match = sizeStr.match(/^(\d+)\s*(B|KB|MB|GB)$/i);
    if (!match) {
      throw new Error(`Invalid size format: ${sizeStr}`);
    }

    const [, size, unit] = match;
    return parseInt(size) * units[unit.toUpperCase()];
  }

  /**
   * Create a child logger with additional metadata
   */
  static createChildLogger(parentLogger: winston.Logger, meta: object): winston.Logger {
    return parentLogger.child(meta);
  }
}
