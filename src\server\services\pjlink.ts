import { Socket } from 'net';
import { createHash } from 'crypto';
import { ProjectorConfig, ProjectorStatus } from '../../types';

export class PJLinkService {
  private config: ProjectorConfig;

  constructor(config: ProjectorConfig) {
    this.config = config;
  }

  /**
   * Send a PJLink command to the projector
   */
  private async sendCommand(command: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const socket = new Socket();
      let response = '';
      let authenticated = false;

      // Set timeout
      socket.setTimeout(this.config.timeout);

      socket.on('connect', () => {
        console.log(`Connected to projector at ${this.config.ip}:${this.config.port}`);
      });

      socket.on('data', (data) => {
        const message = data.toString().trim();
        console.log('Received:', message);

        if (!authenticated) {
          // Handle authentication
          if (message.startsWith('PJLINK 0')) {
            // No authentication required
            authenticated = true;
            socket.write(`%1${command}\r`);
          } else if (message.startsWith('PJLINK 1')) {
            // Authentication required
            const salt = message.split(' ')[1];
            const hash = this.generateAuthHash(salt, this.config.password);
            authenticated = true;
            socket.write(`${hash}%1${command}\r`);
          } else {
            socket.destroy();
            reject(new Error(`Unexpected authentication response: ${message}`));
          }
        } else {
          // Handle command response
          response = message;
          socket.destroy();
        }
      });

      socket.on('close', () => {
        if (authenticated && response) {
          resolve(response);
        } else if (!authenticated) {
          reject(new Error('Connection closed during authentication'));
        } else {
          reject(new Error('Connection closed without response'));
        }
      });

      socket.on('error', (error) => {
        reject(error);
      });

      socket.on('timeout', () => {
        socket.destroy();
        reject(new Error('Connection timeout'));
      });

      // Connect to projector
      socket.connect(this.config.port, this.config.ip);
    });
  }

  /**
   * Generate authentication hash for PJLink
   */
  private generateAuthHash(salt: string, password: string): string {
    const combined = salt + password;
    const hash = createHash('md5').update(combined).digest('hex');
    return hash;
  }

  /**
   * Turn projector power on
   */
  async powerOn(): Promise<boolean> {
    try {
      const response = await this.sendCommand('POWR 1');
      return response.includes('%1POWR=OK') || response.includes('%1POWR=1');
    } catch (error) {
      console.error('Failed to turn projector on:', error);
      return false;
    }
  }

  /**
   * Turn projector power off
   */
  async powerOff(): Promise<boolean> {
    try {
      const response = await this.sendCommand('POWR 0');
      return response.includes('%1POWR=OK') || response.includes('%1POWR=0');
    } catch (error) {
      console.error('Failed to turn projector off:', error);
      return false;
    }
  }

  /**
   * Get projector power status
   */
  async getPowerStatus(): Promise<string> {
    try {
      const response = await this.sendCommand('POWR ?');
      if (response.includes('%1POWR=')) {
        const status = response.split('=')[1];
        switch (status) {
          case '0': return 'off';
          case '1': return 'on';
          case '2': return 'cooling';
          case '3': return 'warming';
          default: return 'unknown';
        }
      }
      return 'unknown';
    } catch (error) {
      console.error('Failed to get projector status:', error);
      return 'unknown';
    }
  }

  /**
   * Get projector input source
   */
  async getInputSource(): Promise<string> {
    try {
      const response = await this.sendCommand('INPT ?');
      if (response.includes('%1INPT=')) {
        return response.split('=')[1];
      }
      return 'unknown';
    } catch (error) {
      console.error('Failed to get input source:', error);
      return 'unknown';
    }
  }

  /**
   * Set projector input source
   */
  async setInputSource(input: string): Promise<boolean> {
    try {
      const response = await this.sendCommand(`INPT ${input}`);
      return response.includes('%1INPT=OK');
    } catch (error) {
      console.error('Failed to set input source:', error);
      return false;
    }
  }

  /**
   * Get lamp hours
   */
  async getLampHours(): Promise<number> {
    try {
      const response = await this.sendCommand('LAMP ?');
      if (response.includes('%1LAMP=')) {
        const lampInfo = response.split('=')[1];
        // Format is usually "hours status" where status is 0 (off) or 1 (on)
        const hours = parseInt(lampInfo.split(' ')[0]);
        return isNaN(hours) ? 0 : hours;
      }
      return 0;
    } catch (error) {
      console.error('Failed to get lamp hours:', error);
      return 0;
    }
  }

  /**
   * Get comprehensive projector status
   */
  async getStatus(): Promise<ProjectorStatus> {
    try {
      const [power, input, lampHours] = await Promise.all([
        this.getPowerStatus(),
        this.getInputSource(),
        this.getLampHours()
      ]);

      return {
        power: power as ProjectorStatus['power'],
        input,
        lampHours,
        temperature: 0 // Temperature requires additional command implementation
      };
    } catch (error) {
      console.error('Failed to get projector status:', error);
      return {
        power: 'unknown',
        input: 'unknown',
        lampHours: 0,
        temperature: 0
      };
    }
  }

  /**
   * Test connection to projector
   */
  async testConnection(): Promise<boolean> {
    try {
      const status = await this.getPowerStatus();
      return status !== 'unknown';
    } catch (error) {
      console.error('Projector connection test failed:', error);
      return false;
    }
  }

  /**
   * Send custom PJLink command
   */
  async sendCustomCommand(command: string): Promise<string> {
    try {
      return await this.sendCommand(command);
    } catch (error) {
      console.error('Failed to send custom command:', error);
      throw error;
    }
  }
}
